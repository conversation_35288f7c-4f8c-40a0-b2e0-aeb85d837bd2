{"name": "eladmin-web", "version": "2.7.0", "description": "ELADMIN 前端源码", "author": "<PERSON>", "license": "Apache-2.0", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "svgo": "svgo -f src/assets/icons/svg --config=src/assets/icons/svgo.yml", "new": "plop"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "repository": {"type": "git", "url": "https://github.com/elunez/eladmin-web.git"}, "bugs": {"url": "https://github.com/elunez/eladmin/issues"}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "@superset-ui/embedded-sdk": "^0.2.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "axios": "1.7.4", "clipboard": "2.0.4", "codemirror": "^5.49.2", "core-js": "^2.6.12", "echarts": "^4.2.1", "echarts-wordcloud": "^1.1.3", "element-ui": "^2.15.8", "file-saver": "1.3.8", "fuse.js": "3.4.4", "js-beautify": "^1.10.2", "js-cookie": "2.2.0", "jsencrypt": "^3.0.0-rc.1", "jszip": "^3.7.1", "mavon-editor": "^2.9.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "8.0.0", "qs": "^6.10.1", "screenfull": "4.2.0", "sortablejs": "1.8.4", "vue": "^2.6.14", "vue-count-to": "^1.0.13", "vue-cropper": "0.4.9", "vue-echarts": "^5.0.0-beta.0", "vue-image-crop-upload": "^2.5.0", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vuedraggable": "2.20.0", "vuex": "3.1.0", "wangeditor": "^4.7.11", "webpack": "^4.47.0"}, "devDependencies": {"@babel/parser": "^7.7.4", "@babel/register": "7.0.0", "@vue/babel-plugin-transform-vue-jsx": "^1.2.1", "@vue/cli-plugin-babel": "3.5.3", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "3.5.3", "@vue/cli-service": "3.5.3", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.0", "babel-plugin-transform-remove-console": "^6.9.4", "chalk": "2.4.2", "chokidar": "2.1.5", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "5.15.3", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "http-proxy-middleware": "3.0.3", "husky": "1.3.1", "lint-staged": "8.1.5", "plop": "2.3.0", "sass": "1.32.13", "sass-loader": "10.2.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "1.16.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "tasksfile": "^5.1.1", "vue-template-compiler": "2.7.15"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}